class Calculator {
    constructor() {
        this.display = document.getElementById('display');
        this.currentInput = '0';
        this.previousInput = '';
        this.operator = '';
        this.waitingForOperand = false;
        this.justCalculated = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupThemeToggle();
        this.setupKeyboardSupport();
    }

    setupEventListeners() {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleButtonClick(e.target);
                this.addPressAnimation(e.target);
            });
        });
    }

    setupThemeToggle() {
        const themeBtn = document.getElementById('theme-btn');
        const savedTheme = localStorage.getItem('calculator-theme') || 'light';
        
        if (savedTheme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
            themeBtn.textContent = '☀️';
        }

        themeBtn.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            themeBtn.textContent = newTheme === 'dark' ? '☀️' : '🌙';
            localStorage.setItem('calculator-theme', newTheme);
        });
    }

    setupKeyboardSupport() {
        document.addEventListener('keydown', (e) => {
            e.preventDefault();
            
            const key = e.key;
            let button = null;

            if (key >= '0' && key <= '9') {
                button = document.querySelector(`[data-value="${key}"]`);
            } else if (key === '.') {
                button = document.querySelector('[data-action="decimal"]');
            } else if (key === '+' || key === '-') {
                button = document.querySelector(`[data-value="${key}"]`);
            } else if (key === '*') {
                button = document.querySelector('[data-value="*"]');
            } else if (key === '/') {
                button = document.querySelector('[data-value="/"]');
            } else if (key === 'Enter' || key === '=') {
                button = document.querySelector('[data-action="equals"]');
            } else if (key === 'Escape' || key === 'c' || key === 'C') {
                button = document.querySelector('[data-action="clear"]');
            } else if (key === 'Backspace') {
                this.backspace();
                return;
            }

            if (button) {
                this.handleButtonClick(button);
                this.addPressAnimation(button);
            }
        });
    }

    handleButtonClick(button) {
        const action = button.dataset.action;
        const value = button.dataset.value;

        switch (action) {
            case 'number':
                this.inputNumber(value);
                break;
            case 'decimal':
                this.inputDecimal();
                break;
            case 'operator':
                this.inputOperator(value);
                break;
            case 'equals':
                this.calculate();
                break;
            case 'clear':
                this.clear();
                break;
        }

        this.updateDisplay();
    }

    inputNumber(num) {
        if (this.waitingForOperand || this.justCalculated) {
            this.currentInput = num;
            this.waitingForOperand = false;
            this.justCalculated = false;
        } else {
            this.currentInput = this.currentInput === '0' ? num : this.currentInput + num;
        }
    }

    inputDecimal() {
        if (this.waitingForOperand || this.justCalculated) {
            this.currentInput = '0.';
            this.waitingForOperand = false;
            this.justCalculated = false;
        } else if (this.currentInput.indexOf('.') === -1) {
            this.currentInput += '.';
        }
    }

    inputOperator(nextOperator) {
        const inputValue = parseFloat(this.currentInput);

        if (this.previousInput === '') {
            this.previousInput = inputValue;
        } else if (this.operator && !this.waitingForOperand) {
            const currentValue = this.previousInput || 0;
            const newValue = this.performCalculation(currentValue, inputValue, this.operator);

            if (newValue === null) return;

            this.currentInput = String(newValue);
            this.previousInput = newValue;
        } else {
            this.previousInput = inputValue;
        }

        this.waitingForOperand = true;
        this.operator = nextOperator;
        this.justCalculated = false;
    }

    calculate() {
        const inputValue = parseFloat(this.currentInput);

        if (this.previousInput !== '' && this.operator && !this.waitingForOperand) {
            const currentValue = this.previousInput || 0;
            const newValue = this.performCalculation(currentValue, inputValue, this.operator);

            if (newValue === null) return;

            this.currentInput = String(newValue);
            this.previousInput = '';
            this.operator = '';
            this.waitingForOperand = false;
            this.justCalculated = true;
        }
    }

    performCalculation(firstOperand, secondOperand, operator) {
        let result;

        switch (operator) {
            case '+':
                result = firstOperand + secondOperand;
                break;
            case '-':
                result = firstOperand - secondOperand;
                break;
            case '*':
                result = firstOperand * secondOperand;
                break;
            case '/':
                if (secondOperand === 0) {
                    this.showError('Cannot divide by zero');
                    return null;
                }
                result = firstOperand / secondOperand;
                break;
            default:
                return null;
        }

        // Round to avoid floating point precision issues
        return Math.round((result + Number.EPSILON) * 100000000) / 100000000;
    }

    clear() {
        this.currentInput = '0';
        this.previousInput = '';
        this.operator = '';
        this.waitingForOperand = false;
        this.justCalculated = false;
    }

    backspace() {
        if (this.currentInput.length > 1) {
            this.currentInput = this.currentInput.slice(0, -1);
        } else {
            this.currentInput = '0';
        }
        this.updateDisplay();
    }

    showError(message) {
        this.display.textContent = message;
        setTimeout(() => {
            this.clear();
            this.updateDisplay();
        }, 2000);
    }

    updateDisplay() {
        let displayValue = this.currentInput;
        
        // Format large numbers with commas
        if (!isNaN(displayValue) && displayValue !== '') {
            const num = parseFloat(displayValue);
            if (Math.abs(num) >= 1000000000) {
                displayValue = num.toExponential(6);
            } else if (Math.abs(num) >= 1000) {
                displayValue = num.toLocaleString();
            }
        }

        this.display.textContent = displayValue;
    }

    addPressAnimation(button) {
        button.classList.add('pressed');
        setTimeout(() => {
            button.classList.remove('pressed');
        }, 100);
    }
}

// Initialize calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new Calculator();
});

// Prevent context menu on right click for better mobile experience
document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
});
