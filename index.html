<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Calculator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- Dark Mode Toggle -->
        <div class="theme-toggle">
            <button id="theme-btn" class="theme-button">🌙</button>
        </div>

        <!-- Calculator -->
        <div class="calculator">
            <div class="display">
                <div class="display-screen" id="display">0</div>
            </div>
            
            <div class="buttons">
                <!-- Row 1 -->
                <button class="btn clear" data-action="clear">C</button>
                <button class="btn operator" data-action="operator" data-value="/">&div;</button>
                <button class="btn operator" data-action="operator" data-value="*">&times;</button>
                <button class="btn operator" data-action="operator" data-value="-">-</button>
                
                <!-- Row 2 -->
                <button class="btn number" data-action="number" data-value="7">7</button>
                <button class="btn number" data-action="number" data-value="8">8</button>
                <button class="btn number" data-action="number" data-value="9">9</button>
                <button class="btn operator" data-action="operator" data-value="+">+</button>
                
                <!-- Row 3 -->
                <button class="btn number" data-action="number" data-value="4">4</button>
                <button class="btn number" data-action="number" data-value="5">5</button>
                <button class="btn number" data-action="number" data-value="6">6</button>
                <button class="btn equals" data-action="equals" rowspan="2">=</button>
                
                <!-- Row 4 -->
                <button class="btn number" data-action="number" data-value="1">1</button>
                <button class="btn number" data-action="number" data-value="2">2</button>
                <button class="btn number" data-action="number" data-value="3">3</button>
                
                <!-- Row 5 -->
                <button class="btn number zero" data-action="number" data-value="0">0</button>
                <button class="btn decimal" data-action="decimal" data-value=".">.</button>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Abdullah G</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
