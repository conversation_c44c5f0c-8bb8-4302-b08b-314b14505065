/* CSS Variables for Light and Dark Themes */
:root {
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --calculator-bg: rgba(255, 255, 255, 0.95);
    --display-bg: #f8f9fa;
    --display-text: #2c3e50;
    --btn-bg: #ffffff;
    --btn-text: #2c3e50;
    --btn-hover: #f1f3f4;
    --btn-active: #e8eaed;
    --operator-bg: #4285f4;
    --operator-text: #ffffff;
    --operator-hover: #3367d6;
    --equals-bg: #34a853;
    --equals-text: #ffffff;
    --equals-hover: #2d8f47;
    --clear-bg: #ea4335;
    --clear-text: #ffffff;
    --clear-hover: #d33b2c;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    --btn-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --footer-text: #ffffff;
}

[data-theme="dark"] {
    --bg-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --calculator-bg: rgba(44, 62, 80, 0.95);
    --display-bg: #34495e;
    --display-text: #ecf0f1;
    --btn-bg: #3c4043;
    --btn-text: #e8eaed;
    --btn-hover: #5f6368;
    --btn-active: #80868b;
    --operator-bg: #1a73e8;
    --operator-text: #ffffff;
    --operator-hover: #1557b0;
    --equals-bg: #137333;
    --equals-text: #ffffff;
    --equals-hover: #0f5132;
    --clear-bg: #d93025;
    --clear-text: #ffffff;
    --clear-hover: #b52d20;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    --btn-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    --footer-text: #bdc3c7;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-gradient);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

.theme-toggle {
    align-self: flex-end;
}

.theme-button {
    background: var(--btn-bg);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 20px;
    cursor: pointer;
    box-shadow: var(--btn-shadow);
    transition: all 0.3s ease;
}

.theme-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.calculator {
    background: var(--calculator-bg);
    border-radius: 20px;
    padding: 25px;
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    max-width: 350px;
    width: 100%;
}

.display {
    background: var(--display-bg);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.display-screen {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--display-text);
    text-align: right;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 12px;
    height: 400px;
}

.btn {
    border: none;
    border-radius: 12px;
    font-size: 1.4rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--btn-shadow);
    background: var(--btn-bg);
    color: var(--btn-text);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn.operator {
    background: var(--operator-bg);
    color: var(--operator-text);
}

.btn.operator:hover {
    background: var(--operator-hover);
}

.btn.equals {
    background: var(--equals-bg);
    color: var(--equals-text);
    grid-row: span 2;
}

.btn.equals:hover {
    background: var(--equals-hover);
}

.btn.clear {
    background: var(--clear-bg);
    color: var(--clear-text);
}

.btn.clear:hover {
    background: var(--clear-hover);
}

.btn.zero {
    grid-column: span 2;
}

.footer {
    text-align: center;
    color: var(--footer-text);
    font-size: 0.9rem;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .calculator {
        padding: 20px;
        max-width: 320px;
    }
    
    .display-screen {
        font-size: 2rem;
        min-height: 50px;
    }
    
    .btn {
        font-size: 1.2rem;
    }
    
    .buttons {
        height: 350px;
        gap: 10px;
    }
}

@media (max-width: 360px) {
    .display-screen {
        font-size: 1.8rem;
    }
    
    .btn {
        font-size: 1.1rem;
    }
    
    .buttons {
        height: 320px;
        gap: 8px;
    }
}

/* Animation for button press */
@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.btn.pressed {
    animation: buttonPress 0.1s ease;
}
