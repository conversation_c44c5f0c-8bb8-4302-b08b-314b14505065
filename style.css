/* CSS Variables for Black and Orange Theme */
:root {
    --bg-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    --calculator-bg: rgba(20, 20, 20, 0.95);
    --display-bg: #0d0d0d;
    --display-text: #ff8c00;
    --btn-bg: #2a2a2a;
    --btn-text: #ffffff;
    --btn-hover: #3a3a3a;
    --btn-active: #4a4a4a;
    --operator-bg: #ff8c00;
    --operator-text: #000000;
    --operator-hover: #ff7700;
    --equals-bg: #ff6600;
    --equals-text: #000000;
    --equals-hover: #ff5500;
    --clear-bg: #ff4400;
    --clear-text: #ffffff;
    --clear-hover: #ff3300;
    --shadow: 0 10px 30px rgba(255, 140, 0, 0.3);
    --btn-shadow: 0 2px 8px rgba(255, 140, 0, 0.2);
    --footer-text: #ff8c00;
}

[data-theme="dark"] {
    --bg-gradient: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    --calculator-bg: rgba(10, 10, 10, 0.98);
    --display-bg: #000000;
    --display-text: #ff9500;
    --btn-bg: #1a1a1a;
    --btn-text: #ffffff;
    --btn-hover: #2a2a2a;
    --btn-active: #3a3a3a;
    --operator-bg: #ff9500;
    --operator-text: #000000;
    --operator-hover: #ff8800;
    --equals-bg: #ff7700;
    --equals-text: #000000;
    --equals-hover: #ff6600;
    --clear-bg: #ff5500;
    --clear-text: #ffffff;
    --clear-hover: #ff4400;
    --shadow: 0 10px 30px rgba(255, 149, 0, 0.4);
    --btn-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
    --footer-text: #ff9500;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-gradient);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

.theme-toggle {
    align-self: flex-end;
}

.theme-button {
    background: var(--operator-bg);
    color: var(--operator-text);
    border: 2px solid var(--operator-bg);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 20px;
    cursor: pointer;
    box-shadow: var(--btn-shadow);
    transition: all 0.3s ease;
}

.theme-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 140, 0, 0.4);
    background: var(--operator-hover);
}

.calculator {
    background: var(--calculator-bg);
    border-radius: 20px;
    padding: 25px;
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    width: 400px;
    min-width: 400px;
    max-width: 90vw;
    box-sizing: border-box;
}

.display {
    background: var(--display-bg);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 140, 0, 0.2);
    border: 1px solid rgba(255, 140, 0, 0.3);
    width: 100%;
    box-sizing: border-box;
}

.display-screen {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--display-text);
    text-align: right;
    height: 60px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 15px;
    height: 420px;
    width: 100%;
    box-sizing: border-box;
}

.btn {
    border: none;
    border-radius: 12px;
    font-size: 1.4rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--btn-shadow);
    background: var(--btn-bg);
    color: var(--btn-text);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
    background: var(--btn-hover);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn.operator {
    background: var(--operator-bg);
    color: var(--operator-text);
}

.btn.operator:hover {
    background: var(--operator-hover);
    box-shadow: 0 4px 12px rgba(255, 140, 0, 0.5);
}

.btn.equals {
    background: var(--equals-bg);
    color: var(--equals-text);
    grid-row: span 2;
}

.btn.equals:hover {
    background: var(--equals-hover);
    box-shadow: 0 4px 12px rgba(255, 102, 0, 0.5);
}

.btn.clear {
    background: var(--clear-bg);
    color: var(--clear-text);
}

.btn.clear:hover {
    background: var(--clear-hover);
}

.btn.zero {
    grid-column: span 2;
}

.footer {
    text-align: center;
    color: var(--footer-text);
    font-size: 0.9rem;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .calculator {
        padding: 20px;
        width: 340px;
        min-width: 340px;
        max-width: 95vw;
        box-sizing: border-box;
    }

    .display-screen {
        font-size: 2rem;
        height: 50px;
    }

    .btn {
        font-size: 1.2rem;
    }

    .buttons {
        height: 360px;
        gap: 12px;
        width: 100%;
        box-sizing: border-box;
    }
}

@media (max-width: 360px) {
    .calculator {
        width: 300px;
        min-width: 300px;
        max-width: 95vw;
        padding: 15px;
        box-sizing: border-box;
    }

    .display-screen {
        font-size: 1.8rem;
        height: 45px;
    }

    .btn {
        font-size: 1.1rem;
    }

    .buttons {
        height: 320px;
        gap: 10px;
        width: 100%;
        box-sizing: border-box;
    }
}

/* Animation for button press */
@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.btn.pressed {
    animation: buttonPress 0.1s ease;
}
